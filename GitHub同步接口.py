#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GitHub同步接口 - 浏览器多账号绿色版 v2.3.0
负责与GitHub API的集成，实现文件的云端存储和同步

作者: 浏览器多账号绿色版开发团队
版本: v2.3.0
日期: 2025-07-25
"""

import os
import json
import base64
import hashlib
import logging
from typing import Dict, List, Optional, Tuple, Any
from urllib.parse import urljoin
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# 配置日志
logger = logging.getLogger(__name__)


class GitHubSyncError(Exception):
    """GitHub同步异常类"""
    pass


class GitHubAPI:
    """GitHub API接口类"""
    
    def __init__(self, token: str, repository: str, branch: str = "main"):
        """
        初始化GitHub API接口
        
        Args:
            token: GitHub访问令牌
            repository: 仓库名称 (格式: owner/repo)
            branch: 分支名称
        """
        self.token = token
        self.repository = repository
        self.branch = branch
        self.base_url = "https://api.github.com"
        
        # 配置HTTP会话
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'token {token}',
            'Accept': 'application/vnd.github.v3+json',
            'User-Agent': 'BrowserManager-CloudSync/2.3.0'
        })
        
        # 配置重试策略
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
    
    def _make_request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """发送HTTP请求"""
        url = urljoin(self.base_url, endpoint)
        
        try:
            response = self.session.request(method, url, **kwargs)
            response.raise_for_status()
            return response
        except requests.exceptions.RequestException as e:
            logger.error(f"GitHub API请求失败: {e}")
            raise GitHubSyncError(f"API请求失败: {e}")
    
    def test_connection(self) -> Tuple[bool, str]:
        """测试GitHub连接和权限"""
        try:
            # 测试仓库访问权限
            endpoint = f"/repos/{self.repository}"
            response = self._make_request("GET", endpoint)
            
            repo_data = response.json()
            if repo_data.get('private', False):
                logger.info("成功连接到私有仓库")
            else:
                logger.info("成功连接到公共仓库")
            
            return True, "连接成功"
            
        except GitHubSyncError as e:
            return False, str(e)
        except Exception as e:
            logger.error(f"连接测试失败: {e}")
            return False, f"连接测试失败: {e}"
    
    def create_repository(self, private: bool = True) -> Tuple[bool, str]:
        """创建仓库（如果不存在）"""
        try:
            # 检查仓库是否存在
            endpoint = f"/repos/{self.repository}"
            try:
                self._make_request("GET", endpoint)
                return True, "仓库已存在"
            except GitHubSyncError:
                pass  # 仓库不存在，继续创建
            
            # 创建新仓库
            repo_name = self.repository.split('/')[-1]
            endpoint = "/user/repos"
            data = {
                "name": repo_name,
                "description": "浏览器多账号绿色版云同步数据",
                "private": private,
                "auto_init": True
            }
            
            response = self._make_request("POST", endpoint, json=data)
            logger.info(f"成功创建仓库: {self.repository}")
            return True, "仓库创建成功"
            
        except Exception as e:
            logger.error(f"创建仓库失败: {e}")
            return False, f"创建仓库失败: {e}"
    
    def get_file_content(self, file_path: str) -> Tuple[Optional[str], Optional[str]]:
        """
        获取文件内容
        
        Args:
            file_path: 文件路径
            
        Returns:
            (content, sha) 元组，如果文件不存在返回 (None, None)
        """
        try:
            endpoint = f"/repos/{self.repository}/contents/{file_path}"
            params = {"ref": self.branch}
            
            response = self._make_request("GET", endpoint, params=params)
            file_data = response.json()
            
            # 解码Base64内容
            content = base64.b64decode(file_data['content']).decode('utf-8')
            sha = file_data['sha']
            
            return content, sha
            
        except GitHubSyncError:
            # 文件不存在
            return None, None
        except Exception as e:
            logger.error(f"获取文件内容失败: {e}")
            raise GitHubSyncError(f"获取文件内容失败: {e}")
    
    def upload_file(self, file_path: str, content: str, message: str, sha: Optional[str] = None) -> bool:
        """
        上传文件到GitHub
        
        Args:
            file_path: 文件路径
            content: 文件内容
            message: 提交消息
            sha: 文件SHA（更新时需要）
            
        Returns:
            是否成功
        """
        try:
            endpoint = f"/repos/{self.repository}/contents/{file_path}"
            
            # 编码内容为Base64
            content_encoded = base64.b64encode(content.encode('utf-8')).decode('ascii')
            
            data = {
                "message": message,
                "content": content_encoded,
                "branch": self.branch
            }
            
            # 如果是更新操作，需要提供SHA
            if sha:
                data["sha"] = sha
            
            response = self._make_request("PUT", endpoint, json=data)
            logger.info(f"文件上传成功: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"文件上传失败: {e}")
            raise GitHubSyncError(f"文件上传失败: {e}")
    
    def download_file(self, file_path: str) -> Optional[str]:
        """
        下载文件内容
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件内容，如果文件不存在返回None
        """
        content, _ = self.get_file_content(file_path)
        return content
    
    def list_files(self, directory: str = "") -> List[Dict[str, Any]]:
        """
        列出目录中的文件
        
        Args:
            directory: 目录路径
            
        Returns:
            文件列表
        """
        try:
            endpoint = f"/repos/{self.repository}/contents/{directory}"
            params = {"ref": self.branch}
            
            response = self._make_request("GET", endpoint, params=params)
            files_data = response.json()
            
            # 确保返回的是列表
            if isinstance(files_data, dict):
                files_data = [files_data]
            
            return files_data
            
        except Exception as e:
            logger.error(f"列出文件失败: {e}")
            return []
    
    def delete_file(self, file_path: str, message: str) -> bool:
        """
        删除文件
        
        Args:
            file_path: 文件路径
            message: 提交消息
            
        Returns:
            是否成功
        """
        try:
            # 先获取文件SHA
            _, sha = self.get_file_content(file_path)
            if not sha:
                logger.warning(f"文件不存在，无法删除: {file_path}")
                return False
            
            endpoint = f"/repos/{self.repository}/contents/{file_path}"
            data = {
                "message": message,
                "sha": sha,
                "branch": self.branch
            }
            
            response = self._make_request("DELETE", endpoint, json=data)
            logger.info(f"文件删除成功: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"文件删除失败: {e}")
            return False
    
    def get_repository_info(self) -> Optional[Dict[str, Any]]:
        """获取仓库信息"""
        try:
            endpoint = f"/repos/{self.repository}"
            response = self._make_request("GET", endpoint)
            return response.json()
        except Exception as e:
            logger.error(f"获取仓库信息失败: {e}")
            return None
    
    def calculate_file_hash(self, content: str) -> str:
        """计算文件内容的哈希值"""
        return hashlib.sha256(content.encode('utf-8')).hexdigest()


class GitHubSyncClient:
    """GitHub同步客户端"""
    
    def __init__(self, token: str, repository: str, branch: str = "main"):
        """初始化GitHub同步客户端"""
        self.api = GitHubAPI(token, repository, branch)
        self.sync_directory = "browser_sync"  # 同步数据存储目录
    
    def test_connection(self) -> Tuple[bool, str]:
        """测试连接"""
        return self.api.test_connection()
    
    def ensure_repository(self) -> Tuple[bool, str]:
        """确保仓库存在"""
        return self.api.create_repository(private=True)
    
    def upload_config(self, config_data: Dict[str, Any], device_id: str) -> bool:
        """上传配置数据"""
        try:
            file_path = f"{self.sync_directory}/config/{device_id}.json"
            content = json.dumps(config_data, ensure_ascii=False, indent=2)
            message = f"更新配置 - 设备: {device_id}"
            
            # 检查文件是否存在
            _, sha = self.api.get_file_content(file_path)
            
            return self.api.upload_file(file_path, content, message, sha)
            
        except Exception as e:
            logger.error(f"上传配置失败: {e}")
            return False
    
    def download_config(self, device_id: str) -> Optional[Dict[str, Any]]:
        """下载配置数据"""
        try:
            file_path = f"{self.sync_directory}/config/{device_id}.json"
            content = self.api.download_file(file_path)
            
            if content:
                return json.loads(content)
            return None
            
        except Exception as e:
            logger.error(f"下载配置失败: {e}")
            return None
    
    def list_device_configs(self) -> List[str]:
        """列出所有设备的配置"""
        try:
            files = self.api.list_files(f"{self.sync_directory}/config")
            device_ids = []
            
            for file_info in files:
                if file_info['name'].endswith('.json'):
                    device_id = file_info['name'][:-5]  # 移除.json后缀
                    device_ids.append(device_id)
            
            return device_ids
            
        except Exception as e:
            logger.error(f"列出设备配置失败: {e}")
            return []


if __name__ == "__main__":
    # 测试代码
    print("GitHub同步接口测试")
    
    # 注意：实际使用时需要提供真实的token和repository
    # token = "your_github_token"
    # repository = "your_username/browser-sync"
    # 
    # client = GitHubSyncClient(token, repository)
    # success, message = client.test_connection()
    # print(f"连接测试: {success} - {message}")
    
    print("请配置GitHub token和repository后进行测试")
