#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
云同步管理器 - 浏览器多账号绿色版 v2.3.0
负责配置和数据的云端同步功能

作者: 浏览器多账号绿色版开发团队
版本: v2.3.0
日期: 2025-07-25
"""

import os
import json
import time
import hashlib
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class SyncStatus(Enum):
    """同步状态枚举"""
    IDLE = "idle"                    # 空闲状态
    UPLOADING = "uploading"          # 上传中
    DOWNLOADING = "downloading"      # 下载中
    SYNCING = "syncing"             # 同步中
    SUCCESS = "success"             # 成功
    ERROR = "error"                 # 错误
    CONFLICT = "conflict"           # 冲突


class SyncType(Enum):
    """同步类型枚举"""
    CONFIG = "config"               # 配置同步
    FILES = "files"                 # 文件同步
    FULL = "full"                   # 完整同步


@dataclass
class SyncConfig:
    """云同步配置数据类"""
    enabled: bool = False                    # 是否启用云同步
    provider: str = "github"                 # 云存储提供商
    repository: str = ""                     # 仓库地址
    branch: str = "main"                     # 分支名称
    token: str = ""                          # 访问令牌
    auto_sync: bool = False                  # 是否自动同步
    sync_interval: int = 300                 # 同步间隔（秒）
    last_sync: Optional[str] = None          # 最后同步时间
    device_id: str = ""                      # 设备ID
    encryption_enabled: bool = True          # 是否启用加密

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SyncConfig':
        """从字典创建实例"""
        return cls(**data)


@dataclass
class SyncRecord:
    """同步记录数据类"""
    timestamp: str                           # 时间戳
    sync_type: str                          # 同步类型
    status: str                             # 同步状态
    message: str                            # 状态消息
    device_id: str                          # 设备ID
    file_count: int = 0                     # 文件数量
    data_size: int = 0                      # 数据大小

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SyncRecord':
        """从字典创建实例"""
        return cls(**data)


class CloudSyncManager:
    """云同步管理器核心类"""

    def __init__(self, config_path: str = "config/cloud_sync.json"):
        """
        初始化云同步管理器

        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config = SyncConfig()
        self.status = SyncStatus.IDLE
        self.sync_records: List[SyncRecord] = []
        self.callbacks = {}  # 状态回调函数

        # 确保配置目录存在
        os.makedirs(os.path.dirname(config_path), exist_ok=True)

        # 加载配置
        self.load_config()

        # 生成设备ID
        if not self.config.device_id:
            self.config.device_id = self._generate_device_id()
            self.save_config()

    def _generate_device_id(self) -> str:
        """生成唯一的设备ID"""
        import platform
        import uuid

        # 基于机器信息生成设备ID
        machine_info = f"{platform.node()}-{platform.machine()}-{uuid.getnode()}"
        device_id = hashlib.md5(machine_info.encode()).hexdigest()[:16]
        return f"device_{device_id}"

    def load_config(self) -> None:
        """加载云同步配置"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.config = SyncConfig.from_dict(data.get('config', {}))

                    # 加载同步记录
                    records_data = data.get('sync_records', [])
                    self.sync_records = [SyncRecord.from_dict(record) for record in records_data]

                logger.info("云同步配置加载成功")
            else:
                logger.info("云同步配置文件不存在，使用默认配置")
        except Exception as e:
            logger.error(f"加载云同步配置失败: {e}")
            self.config = SyncConfig()

    def save_config(self) -> None:
        """保存云同步配置"""
        try:
            data = {
                'config': self.config.to_dict(),
                'sync_records': [record.to_dict() for record in self.sync_records[-100:]]  # 只保留最近100条记录
            }

            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            logger.info("云同步配置保存成功")
        except Exception as e:
            logger.error(f"保存云同步配置失败: {e}")

    def set_status(self, status: SyncStatus, message: str = "") -> None:
        """设置同步状态"""
        self.status = status
        logger.info(f"同步状态变更: {status.value} - {message}")

        # 调用状态回调
        if 'status_changed' in self.callbacks:
            self.callbacks['status_changed'](status, message)

    def add_sync_record(self, sync_type: SyncType, status: SyncStatus, message: str,
                       file_count: int = 0, data_size: int = 0) -> None:
        """添加同步记录"""
        record = SyncRecord(
            timestamp=datetime.now().isoformat(),
            sync_type=sync_type.value,
            status=status.value,
            message=message,
            device_id=self.config.device_id,
            file_count=file_count,
            data_size=data_size
        )

        self.sync_records.append(record)

        # 调用记录回调
        if 'record_added' in self.callbacks:
            self.callbacks['record_added'](record)

    def register_callback(self, event: str, callback) -> None:
        """注册事件回调函数"""
        self.callbacks[event] = callback

    def is_configured(self) -> bool:
        """检查是否已配置云同步"""
        return bool(self.config.enabled and
                   self.config.repository and
                   self.config.token)

    def get_status(self) -> Tuple[SyncStatus, str]:
        """获取当前同步状态"""
        return self.status, self.status.value

    def get_last_sync_time(self) -> Optional[str]:
        """获取最后同步时间"""
        return self.config.last_sync

    def get_sync_records(self, limit: int = 50) -> List[SyncRecord]:
        """获取同步记录"""
        return self.sync_records[-limit:]

    def update_config(self, **kwargs) -> None:
        """更新配置"""
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)

        self.save_config()
        logger.info("云同步配置已更新")

    def validate_config(self) -> Tuple[bool, str]:
        """验证配置有效性"""
        if not self.config.repository:
            return False, "仓库地址不能为空"

        if not self.config.token:
            return False, "访问令牌不能为空"

        if self.config.sync_interval < 60:
            return False, "同步间隔不能少于60秒"

        return True, "配置验证通过"


# 全局云同步管理器实例
cloud_sync_manager = CloudSyncManager()


def get_cloud_sync_manager() -> CloudSyncManager:
    """获取云同步管理器实例"""
    return cloud_sync_manager


if __name__ == "__main__":
    # 测试代码
    manager = CloudSyncManager()

    print(f"设备ID: {manager.config.device_id}")
    print(f"配置状态: {manager.is_configured()}")
    print(f"当前状态: {manager.get_status()}")

    # 测试配置更新
    manager.update_config(
        enabled=True,
        repository="test/browser-sync",
        token="test_token"
    )

    print(f"更新后配置状态: {manager.is_configured()}")

    # 测试同步记录
    manager.add_sync_record(
        SyncType.CONFIG,
        SyncStatus.SUCCESS,
        "测试同步记录",
        file_count=5,
        data_size=1024
    )

    records = manager.get_sync_records()
    print(f"同步记录数量: {len(records)}")
    if records:
        print(f"最新记录: {records[-1].message}")
