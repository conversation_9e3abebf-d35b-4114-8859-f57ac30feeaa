#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
云同步设置对话框 - 浏览器多账号绿色版 v2.3.0
提供云同步功能的配置和管理界面

作者: 浏览器多账号绿色版开发团队
版本: v2.3.0
日期: 2025-07-25
"""

import os
import json
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
from typing import Dict, Any, Optional, Callable

# 导入项目模块
from 云同步管理器 import CloudSyncManager, SyncStatus
from 配置同步器 import ConfigSynchronizer
from GitHub同步接口 import GitHubSyncClient


class CloudSyncSettingsDialog:
    """云同步设置对话框"""

    def __init__(self, parent, sync_manager: CloudSyncManager, language_manager=None, theme_manager=None):
        """
        初始化云同步设置对话框

        Args:
            parent: 父窗口
            sync_manager: 云同步管理器
            language_manager: 语言管理器
            theme_manager: 主题管理器
        """
        self.parent = parent
        self.sync_manager = sync_manager
        self.language_manager = language_manager
        self.theme_manager = theme_manager
        self.synchronizer = ConfigSynchronizer(sync_manager)

        # 创建对话框窗口
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(self._get_text("cloud_sync_settings"))
        self.dialog.geometry("600x500")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 居中显示
        self._center_window()

        # 创建界面
        self._create_widgets()
        self._load_current_settings()

        # 应用主题
        if self.theme_manager:
            self.theme_manager.apply_theme_to_widget(self.dialog)

    def _get_text(self, key: str) -> str:
        """获取多语言文本"""
        if self.language_manager:
            return self.language_manager.get_text(key)

        # 默认文本
        texts = {
            "cloud_sync_settings": "云同步设置",
            "basic_settings": "基础设置",
            "enable_cloud_sync": "启用云同步",
            "github_repository": "GitHub仓库",
            "access_token": "访问令牌",
            "branch_name": "分支名称",
            "sync_options": "同步选项",
            "auto_sync": "自动同步",
            "sync_interval": "同步间隔(秒)",
            "enable_encryption": "启用加密",
            "device_info": "设备信息",
            "device_id": "设备ID",
            "last_sync": "最后同步",
            "sync_actions": "同步操作",
            "test_connection": "测试连接",
            "upload_config": "上传配置",
            "download_config": "下载配置",
            "view_sync_history": "查看同步历史",
            "save": "保存",
            "cancel": "取消",
            "testing_connection": "正在测试连接...",
            "connection_success": "连接成功",
            "connection_failed": "连接失败",
            "uploading_config": "正在上传配置...",
            "upload_success": "上传成功",
            "upload_failed": "上传失败",
            "downloading_config": "正在下载配置...",
            "download_success": "下载成功",
            "download_failed": "下载失败",
            "settings_saved": "设置已保存",
            "invalid_repository": "仓库地址格式无效",
            "invalid_token": "访问令牌不能为空",
            "never": "从未"
        }
        return texts.get(key, key)

    def _center_window(self):
        """窗口居中显示"""
        self.dialog.update_idletasks()
        width = self.dialog.winfo_width()
        height = self.dialog.winfo_height()
        x = (self.dialog.winfo_screenwidth() // 2) - (width // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (height // 2)
        self.dialog.geometry(f"{width}x{height}+{x}+{y}")

    def _create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.dialog, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.dialog.columnconfigure(0, weight=1)
        self.dialog.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        row = 0

        # 基础设置组
        basic_group = ttk.LabelFrame(main_frame, text=self._get_text("basic_settings"), padding="10")
        basic_group.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        basic_group.columnconfigure(1, weight=1)

        # 启用云同步
        self.enable_var = tk.BooleanVar()
        ttk.Checkbutton(basic_group, text=self._get_text("enable_cloud_sync"),
                       variable=self.enable_var, command=self._on_enable_changed).grid(
                       row=0, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))

        # GitHub仓库
        ttk.Label(basic_group, text=self._get_text("github_repository")).grid(
            row=1, column=0, sticky=tk.W, pady=(0, 5))
        self.repository_var = tk.StringVar()
        ttk.Entry(basic_group, textvariable=self.repository_var, width=40).grid(
            row=1, column=1, sticky=(tk.W, tk.E), pady=(0, 5))

        # 访问令牌
        ttk.Label(basic_group, text=self._get_text("access_token")).grid(
            row=2, column=0, sticky=tk.W, pady=(0, 5))
        self.token_var = tk.StringVar()
        token_entry = ttk.Entry(basic_group, textvariable=self.token_var, show="*", width=40)
        token_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=(0, 5))

        # 分支名称
        ttk.Label(basic_group, text=self._get_text("branch_name")).grid(
            row=3, column=0, sticky=tk.W, pady=(0, 5))
        self.branch_var = tk.StringVar(value="main")
        ttk.Entry(basic_group, textvariable=self.branch_var, width=20).grid(
            row=3, column=1, sticky=tk.W, pady=(0, 5))

        row += 1

        # 同步选项组
        sync_group = ttk.LabelFrame(main_frame, text=self._get_text("sync_options"), padding="10")
        sync_group.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        sync_group.columnconfigure(1, weight=1)

        # 自动同步
        self.auto_sync_var = tk.BooleanVar()
        ttk.Checkbutton(sync_group, text=self._get_text("auto_sync"),
                       variable=self.auto_sync_var).grid(
                       row=0, column=0, columnspan=2, sticky=tk.W, pady=(0, 5))

        # 同步间隔
        ttk.Label(sync_group, text=self._get_text("sync_interval")).grid(
            row=1, column=0, sticky=tk.W, pady=(0, 5))
        self.interval_var = tk.IntVar(value=300)
        ttk.Spinbox(sync_group, from_=60, to=3600, textvariable=self.interval_var, width=10).grid(
            row=1, column=1, sticky=tk.W, pady=(0, 5))

        # 启用加密
        self.encryption_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(sync_group, text=self._get_text("enable_encryption"),
                       variable=self.encryption_var).grid(
                       row=2, column=0, columnspan=2, sticky=tk.W, pady=(0, 5))

        row += 1

        # 设备信息组
        device_group = ttk.LabelFrame(main_frame, text=self._get_text("device_info"), padding="10")
        device_group.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        device_group.columnconfigure(1, weight=1)

        # 设备ID
        ttk.Label(device_group, text=self._get_text("device_id")).grid(
            row=0, column=0, sticky=tk.W, pady=(0, 5))
        self.device_id_label = ttk.Label(device_group, text="", foreground="blue")
        self.device_id_label.grid(row=0, column=1, sticky=tk.W, pady=(0, 5))

        # 最后同步时间
        ttk.Label(device_group, text=self._get_text("last_sync")).grid(
            row=1, column=0, sticky=tk.W, pady=(0, 5))
        self.last_sync_label = ttk.Label(device_group, text="")
        self.last_sync_label.grid(row=1, column=1, sticky=tk.W, pady=(0, 5))

        row += 1

        # 同步操作组
        action_group = ttk.LabelFrame(main_frame, text=self._get_text("sync_actions"), padding="10")
        action_group.grid(row=row, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        # 创建按钮框架
        button_frame = ttk.Frame(action_group)
        button_frame.grid(row=0, column=0, sticky=(tk.W, tk.E))

        # 测试连接按钮
        self.test_btn = ttk.Button(button_frame, text=self._get_text("test_connection"),
                                  command=self._test_connection)
        self.test_btn.grid(row=0, column=0, padx=(0, 5))

        # 上传配置按钮
        self.upload_btn = ttk.Button(button_frame, text=self._get_text("upload_config"),
                                    command=self._upload_config)
        self.upload_btn.grid(row=0, column=1, padx=(0, 5))

        # 下载配置按钮
        self.download_btn = ttk.Button(button_frame, text=self._get_text("download_config"),
                                      command=self._download_config)
        self.download_btn.grid(row=0, column=2, padx=(0, 5))

        # 查看历史按钮
        self.history_btn = ttk.Button(button_frame, text=self._get_text("view_sync_history"),
                                     command=self._view_sync_history)
        self.history_btn.grid(row=0, column=3)

        row += 1

        # 状态标签
        self.status_label = ttk.Label(main_frame, text="", foreground="blue")
        self.status_label.grid(row=row, column=0, columnspan=2, pady=(10, 0))

        row += 1

        # 底部按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=row, column=0, columnspan=2, pady=(20, 0))

        ttk.Button(button_frame, text=self._get_text("save"),
                  command=self._save_settings).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(button_frame, text=self._get_text("cancel"),
                  command=self.dialog.destroy).grid(row=0, column=1)

        # 初始状态设置
        self._on_enable_changed()

    def _load_current_settings(self):
        """加载当前设置"""
        config = self.sync_manager.config

        self.enable_var.set(config.enabled)
        self.repository_var.set(config.repository)
        self.token_var.set(config.token)
        self.branch_var.set(config.branch)
        self.auto_sync_var.set(config.auto_sync)
        self.interval_var.set(config.sync_interval)
        self.encryption_var.set(config.encryption_enabled)

        # 设备信息
        self.device_id_label.config(text=config.device_id)

        # 最后同步时间
        if config.last_sync:
            try:
                from datetime import datetime
                last_sync = datetime.fromisoformat(config.last_sync)
                self.last_sync_label.config(text=last_sync.strftime("%Y-%m-%d %H:%M:%S"))
            except:
                self.last_sync_label.config(text=config.last_sync)
        else:
            self.last_sync_label.config(text=self._get_text("never"))

    def _on_enable_changed(self):
        """启用状态改变时的处理"""
        enabled = self.enable_var.get()

        # 启用/禁用相关控件
        state = "normal" if enabled else "disabled"

        # 这里需要遍历所有需要控制的控件
        # 简化处理，实际应用中需要更详细的控件管理

    def _test_connection(self):
        """测试连接"""
        def test_thread():
            try:
                self.status_label.config(text=self._get_text("testing_connection"))
                self.test_btn.config(state="disabled")

                # 创建临时GitHub客户端进行测试
                client = GitHubSyncClient(
                    token=self.token_var.get(),
                    repository=self.repository_var.get(),
                    branch=self.branch_var.get()
                )

                success, message = client.test_connection()

                if success:
                    self.status_label.config(text=self._get_text("connection_success"), foreground="green")
                else:
                    self.status_label.config(text=f"{self._get_text('connection_failed')}: {message}", foreground="red")

            except Exception as e:
                self.status_label.config(text=f"{self._get_text('connection_failed')}: {e}", foreground="red")
            finally:
                self.test_btn.config(state="normal")

        threading.Thread(target=test_thread, daemon=True).start()

    def _upload_config(self):
        """上传配置"""
        def upload_thread():
            try:
                self.status_label.config(text=self._get_text("uploading_config"))
                self.upload_btn.config(state="disabled")

                # 临时保存设置
                self._save_temp_settings()

                success = self.synchronizer.upload_config()

                if success:
                    self.status_label.config(text=self._get_text("upload_success"), foreground="green")
                    self._load_current_settings()  # 刷新显示
                else:
                    self.status_label.config(text=self._get_text("upload_failed"), foreground="red")

            except Exception as e:
                self.status_label.config(text=f"{self._get_text('upload_failed')}: {e}", foreground="red")
            finally:
                self.upload_btn.config(state="normal")

        threading.Thread(target=upload_thread, daemon=True).start()

    def _download_config(self):
        """下载配置"""
        def download_thread():
            try:
                self.status_label.config(text=self._get_text("downloading_config"))
                self.download_btn.config(state="disabled")

                # 临时保存设置
                self._save_temp_settings()

                success = self.synchronizer.download_config()

                if success:
                    self.status_label.config(text=self._get_text("download_success"), foreground="green")
                    self._load_current_settings()  # 刷新显示
                else:
                    self.status_label.config(text=self._get_text("download_failed"), foreground="red")

            except Exception as e:
                self.status_label.config(text=f"{self._get_text('download_failed')}: {e}", foreground="red")
            finally:
                self.download_btn.config(state="normal")

        threading.Thread(target=download_thread, daemon=True).start()

    def _view_sync_history(self):
        """查看同步历史"""
        # 这里可以打开一个新的对话框显示同步历史
        records = self.sync_manager.get_sync_records()

        history_text = "同步历史记录:\n\n"
        for record in records[-10:]:  # 显示最近10条记录
            history_text += f"{record.timestamp}: {record.message}\n"

        messagebox.showinfo("同步历史", history_text)

    def _save_temp_settings(self):
        """临时保存设置（用于测试和同步操作）"""
        self.sync_manager.update_config(
            enabled=self.enable_var.get(),
            repository=self.repository_var.get(),
            token=self.token_var.get(),
            branch=self.branch_var.get(),
            auto_sync=self.auto_sync_var.get(),
            sync_interval=self.interval_var.get(),
            encryption_enabled=self.encryption_var.get()
        )

    def _save_settings(self):
        """保存设置"""
        try:
            # 验证输入
            if self.enable_var.get():
                if not self.repository_var.get():
                    messagebox.showerror("错误", self._get_text("invalid_repository"))
                    return

                if not self.token_var.get():
                    messagebox.showerror("错误", self._get_text("invalid_token"))
                    return

            # 保存设置
            self._save_temp_settings()

            messagebox.showinfo("成功", self._get_text("settings_saved"))
            self.dialog.destroy()

        except Exception as e:
            messagebox.showerror("错误", f"保存设置失败: {e}")


class CloudSyncStatusPanel:
    """云同步状态面板"""

    def __init__(self, parent, sync_manager: CloudSyncManager, language_manager=None):
        """
        初始化云同步状态面板

        Args:
            parent: 父容器
            sync_manager: 云同步管理器
            language_manager: 语言管理器
        """
        self.parent = parent
        self.sync_manager = sync_manager
        self.language_manager = language_manager

        # 创建面板框架
        self.frame = ttk.LabelFrame(parent, text=self._get_text("cloud_sync_status"), padding="5")

        # 创建状态显示组件
        self._create_status_widgets()

        # 注册状态回调
        self.sync_manager.register_callback('status_changed', self._on_status_changed)

        # 初始化状态显示
        self._update_status_display()

    def _get_text(self, key: str) -> str:
        """获取多语言文本"""
        if self.language_manager:
            return self.language_manager.get_text(key)

        texts = {
            "cloud_sync_status": "云同步状态",
            "status": "状态",
            "last_sync": "最后同步",
            "quick_sync": "快速同步",
            "sync_settings": "同步设置",
            "idle": "空闲",
            "syncing": "同步中",
            "success": "成功",
            "error": "错误",
            "never": "从未"
        }
        return texts.get(key, key)

    def _create_status_widgets(self):
        """创建状态显示组件"""
        # 状态指示器
        status_frame = ttk.Frame(self.frame)
        status_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=5, pady=2)

        ttk.Label(status_frame, text=self._get_text("status") + ":").grid(row=0, column=0, sticky=tk.W)
        self.status_label = ttk.Label(status_frame, text=self._get_text("idle"))
        self.status_label.grid(row=0, column=1, sticky=tk.W, padx=(5, 0))

        # 最后同步时间
        time_frame = ttk.Frame(self.frame)
        time_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), padx=5, pady=2)

        ttk.Label(time_frame, text=self._get_text("last_sync") + ":").grid(row=0, column=0, sticky=tk.W)
        self.time_label = ttk.Label(time_frame, text=self._get_text("never"))
        self.time_label.grid(row=0, column=1, sticky=tk.W, padx=(5, 0))

        # 操作按钮
        button_frame = ttk.Frame(self.frame)
        button_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), padx=5, pady=5)

        self.sync_btn = ttk.Button(button_frame, text=self._get_text("quick_sync"),
                                  command=self._quick_sync, width=12)
        self.sync_btn.grid(row=0, column=0, padx=(0, 5))

        ttk.Button(button_frame, text=self._get_text("sync_settings"),
                  command=self._open_settings, width=12).grid(row=0, column=1)

    def _update_status_display(self):
        """更新状态显示"""
        status, _ = self.sync_manager.get_status()
        self.status_label.config(text=self._get_text(status.value))

        # 根据状态设置颜色
        if status == SyncStatus.SUCCESS:
            self.status_label.config(foreground="green")
        elif status == SyncStatus.ERROR:
            self.status_label.config(foreground="red")
        elif status in [SyncStatus.UPLOADING, SyncStatus.DOWNLOADING, SyncStatus.SYNCING]:
            self.status_label.config(foreground="blue")
        else:
            self.status_label.config(foreground="black")

        # 更新最后同步时间
        last_sync = self.sync_manager.get_last_sync_time()
        if last_sync:
            try:
                from datetime import datetime
                sync_time = datetime.fromisoformat(last_sync)
                self.time_label.config(text=sync_time.strftime("%m-%d %H:%M"))
            except:
                self.time_label.config(text=last_sync[:16])
        else:
            self.time_label.config(text=self._get_text("never"))

    def _on_status_changed(self, status: SyncStatus, message: str):
        """状态变化回调"""
        self._update_status_display()

    def _quick_sync(self):
        """快速同步"""
        def sync_thread():
            try:
                self.sync_btn.config(state="disabled")

                # 执行上传同步
                synchronizer = ConfigSynchronizer(self.sync_manager)
                synchronizer.upload_config()

            except Exception as e:
                print(f"快速同步失败: {e}")
            finally:
                self.sync_btn.config(state="normal")

        if self.sync_manager.is_configured():
            threading.Thread(target=sync_thread, daemon=True).start()
        else:
            self._open_settings()

    def _open_settings(self):
        """打开设置对话框"""
        CloudSyncSettingsDialog(self.parent, self.sync_manager, self.language_manager)

    def grid(self, **kwargs):
        """网格布局"""
        self.frame.grid(**kwargs)

    def pack(self, **kwargs):
        """包装布局"""
        self.frame.pack(**kwargs)


if __name__ == "__main__":
    # 测试代码
    from 云同步管理器 import get_cloud_sync_manager

    root = tk.Tk()
    root.title("云同步测试")
    root.geometry("400x300")

    manager = get_cloud_sync_manager()

    # 测试设置对话框
    def test_settings():
        CloudSyncSettingsDialog(root, manager)

    # 测试状态面板
    status_panel = CloudSyncStatusPanel(root, manager)
    status_panel.pack(fill=tk.X, padx=10, pady=10)

    ttk.Button(root, text="打开设置", command=test_settings).pack(pady=10)

    root.mainloop()
