#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单云同步功能测试 - 浏览器多账号绿色版 v2.3.0
简化的云同步功能测试

作者: 浏览器多账号绿色版开发团队
版本: v2.3.0
日期: 2025-07-25
"""

import os
import sys
import json
import tempfile
import shutil

def test_cloud_sync_manager():
    """测试云同步管理器"""
    print("🧪 测试云同步管理器...")

    try:
        from 云同步管理器 import CloudSyncManager, SyncStatus, SyncType

        # 创建临时配置文件
        temp_dir = tempfile.mkdtemp()
        config_path = os.path.join(temp_dir, "test_config.json")

        # 创建管理器实例
        manager = CloudSyncManager(config_path)

        # 测试设备ID生成
        assert manager.config.device_id is not None
        assert manager.config.device_id.startswith("device_")
        print("  ✓ 设备ID生成正常")

        # 测试配置更新
        manager.update_config(
            enabled=True,
            repository="test/repo",
            token="test_token"
        )
        assert manager.config.enabled == True
        assert manager.config.repository == "test/repo"
        print("  ✓ 配置更新正常")

        # 测试同步记录
        manager.add_sync_record(
            SyncType.CONFIG,
            SyncStatus.SUCCESS,
            "测试同步记录"
        )
        records = manager.get_sync_records()
        assert len(records) == 1
        assert records[0].message == "测试同步记录"
        print("  ✓ 同步记录管理正常")

        # 清理
        shutil.rmtree(temp_dir, ignore_errors=True)

        print("✅ 云同步管理器测试通过")
        return True

    except Exception as e:
        print(f"❌ 云同步管理器测试失败: {e}")
        return False


def test_github_api():
    """测试GitHub API"""
    print("\n🧪 测试GitHub API...")

    try:
        from GitHub同步接口 import GitHubAPI, GitHubSyncClient

        # 创建API实例（使用测试参数）
        api = GitHubAPI("test_token", "test/repo", "main")

        # 测试基础属性
        assert api.token == "test_token"
        assert api.repository == "test/repo"
        assert api.branch == "main"
        print("  ✓ API实例创建正常")

        # 测试哈希计算
        test_content = "test content"
        hash_value = api.calculate_file_hash(test_content)
        assert len(hash_value) == 64  # SHA256哈希长度
        print("  ✓ 文件哈希计算正常")

        # 创建同步客户端
        client = GitHubSyncClient("test_token", "test/repo")
        assert client.api.repository == "test/repo"
        print("  ✓ 同步客户端创建正常")

        print("✅ GitHub API测试通过")
        return True

    except Exception as e:
        print(f"❌ GitHub API测试失败: {e}")
        return False


def test_config_synchronizer():
    """测试配置同步器"""
    print("\n🧪 测试配置同步器...")

    try:
        from 云同步管理器 import CloudSyncManager
        from 配置同步器 import ConfigSynchronizer, SyncData, AppConfig, BrowserInstance

        # 创建临时环境
        temp_dir = tempfile.mkdtemp()
        config_path = os.path.join(temp_dir, "test_config.json")

        # 创建管理器和同步器
        manager = CloudSyncManager(config_path)
        synchronizer = ConfigSynchronizer(manager)

        # 测试数据结构
        app_config = AppConfig(theme="dark", language="en_US")
        browser_instance = BrowserInstance(
            name="测试浏览器",
            icon_type="test",
            icon_path="/path/to/icon",
            data_directory="/path/to/data",
            created_time="2025-07-25T10:00:00"
        )

        sync_data = SyncData(
            version="2.3.0",
            timestamp="2025-07-25T10:00:00",
            device_id="test_device",
            app_config=app_config,
            browser_instances=[browser_instance],
            metadata={"test": "data"}
        )

        # 测试序列化
        data_dict = sync_data.to_dict()
        assert data_dict["version"] == "2.3.0"
        assert data_dict["device_id"] == "test_device"
        print("  ✓ 数据序列化正常")

        # 测试反序列化
        restored_data = SyncData.from_dict(data_dict)
        assert restored_data.version == sync_data.version
        assert restored_data.device_id == sync_data.device_id
        assert restored_data.app_config.theme == sync_data.app_config.theme
        assert len(restored_data.browser_instances) == 1
        print("  ✓ 数据反序列化正常")

        # 清理
        shutil.rmtree(temp_dir, ignore_errors=True)

        print("✅ 配置同步器测试通过")
        return True

    except Exception as e:
        print(f"❌ 配置同步器测试失败: {e}")
        return False


def test_ui_components():
    """测试UI组件"""
    print("\n🧪 测试UI组件...")

    try:
        # 测试导入
        from 云同步设置对话框 import CloudSyncSettingsDialog, CloudSyncStatusPanel
        print("  ✓ UI组件导入正常")

        # 注意：这里不创建实际的UI实例，只测试类定义
        assert CloudSyncSettingsDialog is not None
        assert CloudSyncStatusPanel is not None
        print("  ✓ UI组件类定义正常")

        print("✅ UI组件测试通过")
        return True

    except Exception as e:
        print(f"❌ UI组件测试失败: {e}")
        return False


def test_integration():
    """集成测试"""
    print("\n🧪 集成测试...")

    try:
        from 云同步管理器 import CloudSyncManager
        from 配置同步器 import ConfigSynchronizer

        # 创建临时环境
        temp_dir = tempfile.mkdtemp()
        config_path = os.path.join(temp_dir, "test_config.json")

        # 创建必要的目录结构
        config_dir = os.path.join(temp_dir, "config")
        browsers_dir = os.path.join(temp_dir, "浏览器实例")
        os.makedirs(config_dir, exist_ok=True)
        os.makedirs(browsers_dir, exist_ok=True)

        # 创建完整的同步系统
        manager = CloudSyncManager(config_path)
        synchronizer = ConfigSynchronizer(manager)

        # 设置正确的路径
        synchronizer.local_config_path = config_dir
        synchronizer.browsers_path = browsers_dir

        # 配置系统
        manager.update_config(
            enabled=True,
            repository="test/browser-sync",
            token="test_token_123",
            auto_sync=False
        )

        # 验证配置
        print(f"  Debug: enabled={manager.config.enabled}, repo='{manager.config.repository}', token='{manager.config.token}'")
        print(f"  Debug: enabled type={type(manager.config.enabled)}, repo type={type(manager.config.repository)}")
        print(f"  Debug: bool(enabled)={bool(manager.config.enabled)}, bool(repo)={bool(manager.config.repository)}, bool(token)={bool(manager.config.token)}")

        assert manager.config.enabled == True
        assert manager.config.repository == "test/browser-sync"
        assert manager.config.token == "test_token_123"

        # 手动检查is_configured逻辑
        is_configured_manual = (manager.config.enabled and
                               manager.config.repository and
                               manager.config.token)
        print(f"  Debug: manual is_configured={is_configured_manual}")
        print(f"  Debug: method is_configured={manager.is_configured()}")

        assert manager.is_configured() == True
        print("  ✓ 系统配置正常")

        # 测试配置收集
        sync_data = synchronizer.collect_local_config()
        assert sync_data.device_id == manager.config.device_id
        assert sync_data.version == "2.3.0"
        print("  ✓ 配置收集正常")

        # 清理
        shutil.rmtree(temp_dir, ignore_errors=True)

        print("✅ 集成测试通过")
        return True

    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🌟 云同步MVP功能测试开始")
    print("=" * 50)

    test_results = []

    # 运行各项测试
    test_results.append(test_cloud_sync_manager())
    test_results.append(test_github_api())
    test_results.append(test_config_synchronizer())
    test_results.append(test_ui_components())
    test_results.append(test_integration())

    # 统计结果
    passed = sum(test_results)
    total = len(test_results)

    print("\n" + "=" * 50)
    print("📊 测试结果统计")
    print(f"总测试数: {total}")
    print(f"通过数: {passed}")
    print(f"失败数: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")

    if passed == total:
        print("\n🎉 所有测试通过！MVP功能开发完成！")
        print("\n✅ 功能验证:")
        print("  • 云同步管理器核心功能正常")
        print("  • GitHub API集成功能正常")
        print("  • 配置同步器功能正常")
        print("  • UI组件定义正常")
        print("  • 系统集成功能正常")
        print("\n🚀 可以继续进行第二阶段开发！")
        return True
    else:
        print(f"\n❌ 有 {total - passed} 个测试失败，需要修复后再继续")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
