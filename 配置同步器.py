#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置同步器 - 浏览器多账号绿色版 v2.3.0
负责浏览器配置的序列化、同步和合并

作者: 浏览器多账号绿色版开发团队
版本: v2.3.0
日期: 2025-07-25
"""

import os
import json
import time
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from dataclasses import dataclass, asdict

# 导入项目模块
from 云同步管理器 import CloudSyncManager, SyncStatus, SyncType
from GitHub同步接口 import GitHubSyncClient, GitHubSyncError

# 配置日志
logger = logging.getLogger(__name__)


@dataclass
class BrowserInstance:
    """浏览器实例配置"""
    name: str                           # 浏览器名称
    icon_type: str                      # 图标类型
    icon_path: str                      # 图标路径
    data_directory: str                 # 数据目录
    created_time: str                   # 创建时间
    last_used: Optional[str] = None     # 最后使用时间
    custom_args: List[str] = None       # 自定义启动参数
    
    def __post_init__(self):
        if self.custom_args is None:
            self.custom_args = []


@dataclass
class AppConfig:
    """应用程序配置"""
    theme: str = "light"                # 主题设置
    language: str = "zh_CN"             # 语言设置
    auto_start: bool = False            # 自动启动
    window_geometry: str = ""           # 窗口几何信息
    last_selected_browser: str = ""     # 最后选择的浏览器
    
    # 功能开关
    enable_plugin_sync: bool = True     # 启用插件同步
    enable_icon_download: bool = True   # 启用图标下载
    enable_auto_update: bool = True     # 启用自动更新


@dataclass
class SyncData:
    """同步数据结构"""
    version: str                        # 数据版本
    timestamp: str                      # 时间戳
    device_id: str                      # 设备ID
    app_config: AppConfig               # 应用配置
    browser_instances: List[BrowserInstance]  # 浏览器实例列表
    metadata: Dict[str, Any]            # 元数据
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'version': self.version,
            'timestamp': self.timestamp,
            'device_id': self.device_id,
            'app_config': asdict(self.app_config),
            'browser_instances': [asdict(instance) for instance in self.browser_instances],
            'metadata': self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SyncData':
        """从字典创建实例"""
        app_config = AppConfig(**data.get('app_config', {}))
        
        browser_instances = []
        for instance_data in data.get('browser_instances', []):
            browser_instances.append(BrowserInstance(**instance_data))
        
        return cls(
            version=data.get('version', '1.0.0'),
            timestamp=data.get('timestamp', ''),
            device_id=data.get('device_id', ''),
            app_config=app_config,
            browser_instances=browser_instances,
            metadata=data.get('metadata', {})
        )


class ConfigSynchronizer:
    """配置同步器"""
    
    def __init__(self, sync_manager: CloudSyncManager):
        """
        初始化配置同步器
        
        Args:
            sync_manager: 云同步管理器实例
        """
        self.sync_manager = sync_manager
        self.github_client: Optional[GitHubSyncClient] = None
        self.local_config_path = "config"
        self.browsers_path = "浏览器实例"
        
        # 确保目录存在
        os.makedirs(self.local_config_path, exist_ok=True)
        os.makedirs(self.browsers_path, exist_ok=True)
    
    def _init_github_client(self) -> bool:
        """初始化GitHub客户端"""
        try:
            config = self.sync_manager.config
            if not config.repository or not config.token:
                logger.error("GitHub配置不完整")
                return False
            
            self.github_client = GitHubSyncClient(
                token=config.token,
                repository=config.repository,
                branch=config.branch
            )
            
            return True
        except Exception as e:
            logger.error(f"初始化GitHub客户端失败: {e}")
            return False
    
    def collect_local_config(self) -> SyncData:
        """收集本地配置数据"""
        try:
            # 读取应用配置
            app_config = self._load_app_config()
            
            # 读取浏览器实例配置
            browser_instances = self._load_browser_instances()
            
            # 创建同步数据
            sync_data = SyncData(
                version="2.3.0",
                timestamp=datetime.now().isoformat(),
                device_id=self.sync_manager.config.device_id,
                app_config=app_config,
                browser_instances=browser_instances,
                metadata={
                    'total_browsers': len(browser_instances),
                    'sync_type': 'full',
                    'platform': os.name
                }
            )
            
            logger.info(f"收集本地配置完成，浏览器实例数量: {len(browser_instances)}")
            return sync_data
            
        except Exception as e:
            logger.error(f"收集本地配置失败: {e}")
            raise
    
    def _load_app_config(self) -> AppConfig:
        """加载应用配置"""
        config_file = os.path.join(self.local_config_path, "app_config.json")
        
        try:
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return AppConfig(**data)
            else:
                logger.info("应用配置文件不存在，使用默认配置")
                return AppConfig()
        except Exception as e:
            logger.error(f"加载应用配置失败: {e}")
            return AppConfig()
    
    def _load_browser_instances(self) -> List[BrowserInstance]:
        """加载浏览器实例配置"""
        instances = []
        
        try:
            if not os.path.exists(self.browsers_path):
                return instances
            
            for item in os.listdir(self.browsers_path):
                item_path = os.path.join(self.browsers_path, item)
                if os.path.isdir(item_path):
                    instance = self._load_browser_instance(item, item_path)
                    if instance:
                        instances.append(instance)
            
            logger.info(f"加载浏览器实例完成，数量: {len(instances)}")
            return instances
            
        except Exception as e:
            logger.error(f"加载浏览器实例失败: {e}")
            return instances
    
    def _load_browser_instance(self, name: str, path: str) -> Optional[BrowserInstance]:
        """加载单个浏览器实例配置"""
        try:
            # 查找图标文件
            icon_path = ""
            icon_type = "generic"
            
            for file in os.listdir(path):
                if file.endswith('.ico'):
                    icon_path = os.path.join(path, file)
                    icon_type = file.replace('.ico', '')
                    break
            
            # 获取创建时间
            created_time = datetime.fromtimestamp(os.path.getctime(path)).isoformat()
            
            # 查找数据目录
            data_directory = ""
            for item in os.listdir(path):
                if item.startswith('Data_'):
                    data_directory = os.path.join(path, item)
                    break
            
            return BrowserInstance(
                name=name,
                icon_type=icon_type,
                icon_path=icon_path,
                data_directory=data_directory,
                created_time=created_time
            )
            
        except Exception as e:
            logger.error(f"加载浏览器实例失败 {name}: {e}")
            return None
    
    def upload_config(self) -> bool:
        """上传配置到云端"""
        try:
            if not self._init_github_client():
                return False
            
            self.sync_manager.set_status(SyncStatus.UPLOADING, "正在上传配置...")
            
            # 收集本地配置
            sync_data = self.collect_local_config()
            
            # 上传到GitHub
            success = self.github_client.upload_config(
                sync_data.to_dict(),
                self.sync_manager.config.device_id
            )
            
            if success:
                # 更新最后同步时间
                self.sync_manager.config.last_sync = datetime.now().isoformat()
                self.sync_manager.save_config()
                
                self.sync_manager.set_status(SyncStatus.SUCCESS, "配置上传成功")
                self.sync_manager.add_sync_record(
                    SyncType.CONFIG,
                    SyncStatus.SUCCESS,
                    "配置上传成功",
                    file_count=len(sync_data.browser_instances)
                )
                return True
            else:
                self.sync_manager.set_status(SyncStatus.ERROR, "配置上传失败")
                return False
                
        except Exception as e:
            logger.error(f"上传配置失败: {e}")
            self.sync_manager.set_status(SyncStatus.ERROR, f"上传失败: {e}")
            return False
    
    def download_config(self, device_id: Optional[str] = None) -> bool:
        """从云端下载配置"""
        try:
            if not self._init_github_client():
                return False
            
            # 使用指定设备ID或当前设备ID
            target_device_id = device_id or self.sync_manager.config.device_id
            
            self.sync_manager.set_status(SyncStatus.DOWNLOADING, f"正在下载配置 (设备: {target_device_id})...")
            
            # 从GitHub下载配置
            config_data = self.github_client.download_config(target_device_id)
            
            if config_data:
                sync_data = SyncData.from_dict(config_data)
                
                # 应用配置（如果是当前设备或用户确认）
                if device_id is None or device_id == self.sync_manager.config.device_id:
                    self._apply_config(sync_data)
                
                self.sync_manager.set_status(SyncStatus.SUCCESS, "配置下载成功")
                self.sync_manager.add_sync_record(
                    SyncType.CONFIG,
                    SyncStatus.SUCCESS,
                    f"配置下载成功 (设备: {target_device_id})",
                    file_count=len(sync_data.browser_instances)
                )
                return True
            else:
                self.sync_manager.set_status(SyncStatus.ERROR, "未找到配置数据")
                return False
                
        except Exception as e:
            logger.error(f"下载配置失败: {e}")
            self.sync_manager.set_status(SyncStatus.ERROR, f"下载失败: {e}")
            return False
    
    def _apply_config(self, sync_data: SyncData) -> None:
        """应用下载的配置"""
        try:
            # 保存应用配置
            config_file = os.path.join(self.local_config_path, "app_config.json")
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(asdict(sync_data.app_config), f, ensure_ascii=False, indent=2)
            
            # 注意：浏览器实例的应用需要更复杂的逻辑，这里只是示例
            logger.info("配置应用完成")
            
        except Exception as e:
            logger.error(f"应用配置失败: {e}")
            raise
    
    def list_available_configs(self) -> List[str]:
        """列出可用的配置（所有设备）"""
        try:
            if not self._init_github_client():
                return []
            
            return self.github_client.list_device_configs()
            
        except Exception as e:
            logger.error(f"列出可用配置失败: {e}")
            return []


if __name__ == "__main__":
    # 测试代码
    from 云同步管理器 import get_cloud_sync_manager
    
    manager = get_cloud_sync_manager()
    synchronizer = ConfigSynchronizer(manager)
    
    # 测试收集本地配置
    try:
        sync_data = synchronizer.collect_local_config()
        print(f"设备ID: {sync_data.device_id}")
        print(f"浏览器实例数量: {len(sync_data.browser_instances)}")
        print(f"应用配置主题: {sync_data.app_config.theme}")
    except Exception as e:
        print(f"测试失败: {e}")
